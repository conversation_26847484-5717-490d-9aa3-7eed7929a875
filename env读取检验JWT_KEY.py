#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# 读取.env文件并检验JWT_KEY是否正确

import os
import jwt
import sys
import requests
from dotenv import load_dotenv

def load_env_file(file_path=".env"):
    """加载.env文件并返回环境变量字典"""
    if not os.path.exists(file_path):
        print(f"错误: 找不到环境变量文件 {file_path}")
        sys.exit(1)
    
    load_dotenv(file_path)
    
    env_vars = {
        "SUPABASE_URL": os.getenv("SUPABASE_URL"),
        "SUPABASE_ANON_KEY": os.getenv("SUPABASE_ANON_KEY"),
        "JWT_SECRET": os.getenv("JWT_SECRET")
    }
    
    # 检查必要的环境变量是否存在
    for key, value in env_vars.items():
        if not value:
            print(f"错误: 环境变量 {key} 未设置或为空")
    
    return env_vars

def verify_jwt(token, secret):
    """验证JWT令牌是否使用给定的密钥签名"""
    try:
        # 解码JWT，不验证过期时间（因为这些通常是长期令牌）
        decoded = jwt.decode(
            token, 
            secret, 
            algorithms=["HS256"],
            options={"verify_exp": False}
        )
        return True, decoded
    except jwt.InvalidSignatureError:
        return False, "签名无效 - JWT_SECRET与用于签名令牌的密钥不匹配"
    except jwt.DecodeError:
        return False, "解码错误 - 令牌格式可能不正确"
    except Exception as e:
        return False, f"验证时出错: {str(e)}"

def check_supabase_connectivity(url, anon_key):
    """检查Supabase连接性"""
    try:
        headers = {
            "apikey": anon_key,
            "Authorization": f"Bearer {anon_key}"
        }
        
        print(f"尝试连接URL: {url}/rest")
        print(f"使用Headers: {headers}")
        
        # 尝试简单的健康检查请求
        response = requests.get(f"{url}/rest", headers=headers, timeout=5)
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            return True, "连接成功"
        else:
            return False, f"HTTP错误: {response.status_code} - {response.text}"
    except requests.exceptions.RequestException as e:
        return False, f"连接错误: {str(e)}"

def main():
    # 加载环境变量
    print("正在加载环境变量...")
    env = load_env_file()
    
    print("\n===== 环境变量信息 =====")
    print(f"SUPABASE_URL: {env['SUPABASE_URL']}")
    print(f"JWT_SECRET长度: {len(env['JWT_SECRET']) if env['JWT_SECRET'] else 0}")
    print(f"ANON_KEY长度: {len(env['SUPABASE_ANON_KEY']) if env['SUPABASE_ANON_KEY'] else 0}")
    
    # 验证ANON_KEY
    anon_key = env['SUPABASE_ANON_KEY']
    jwt_secret = env['JWT_SECRET']
    
    if anon_key and jwt_secret:
        print("\n===== JWT验证 =====")
        success, result = verify_jwt(anon_key, jwt_secret)
        
        if success:
            print("✅ JWT验证成功!")
            print(f"解码后的数据: {result}")
        else:
            print(f"❌ JWT验证失败: {result}")
            print("\n可能的解决方案:")
            print("1. 确保JWT_SECRET与Supabase服务器上的JWT_SECRET完全相同")
            print("2. 检查JWT_SECRET是否包含任何额外的空格或换行符")
            print("3. 如果您的Supabase是自托管的，检查docker-compose配置中的JWT_SECRET")
    
    # 检查Supabase连接
    if env['SUPABASE_URL'] and env['SUPABASE_ANON_KEY']:
        print("\n===== Supabase连接测试 =====")
        success, result = check_supabase_connectivity(env['SUPABASE_URL'], env['SUPABASE_ANON_KEY'])
        
        if success:
            print(f"✅ Supabase连接成功: {result}")
        else:
            print(f"❌ Supabase连接失败: {result}")
    
    print("\n提示: 如果JWT验证失败，您需要确保.env文件中的JWT_SECRET与Supabase服务器上的完全一致。")
    print("对于自托管Supabase，请检查服务器上的docker-compose.yml文件或环境配置。")

if __name__ == "__main__":
    main() 