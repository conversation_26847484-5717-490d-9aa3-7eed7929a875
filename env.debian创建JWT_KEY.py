#!/usr/bin/env python3
# Supabase JWT Generator  – v2-ready (sub / iat / nbf / exp)
# Created: 2025-04-30 Updated: 2025-05-01

import secrets
import base64
import uuid
import jwt
from datetime import datetime, timedelta, timezone

# ---------- 生成 256-bit 随机密钥（去掉末尾 =） ----------
def generate_jwt_secret() -> str:
    while True:
        s = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode().rstrip("=")
        if not s.endswith("="):
            return s

# ---------- 生成带 sub / iat / nbf / exp 的 HS256 Token ----------
def make_token(secret: str, role: str, years: int = 100) -> str:
    now  = datetime.now(tz=timezone.utc)
    exp  = now + timedelta(days=365 * years)
    pay  = {
        "iss":  "supabase",
        "sub":  str(uuid.uuid4()),   # 随机主语
        "role": role,                # anon / service_role
        "iat":  int(now.timestamp()),
        "nbf":  int(now.timestamp()),
        "exp":  int(exp.timestamp()),
    }
    return jwt.encode(pay, secret, algorithm="HS256")

# ---------- 主流程 ----------
def main() -> None:
    secret       = generate_jwt_secret()
    anon_token   = make_token(secret, "anon")
    service_token= make_token(secret, "service_role")

    env_text = f"""# Supabase JWT Configuration – v2 ready
# Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
JWT_SECRET={secret}
SUPABASE_ANON_KEY={anon_token}
SUPABASE_SERVICE_KEY={service_token}
"""

    # 写新 .env.debian（Unix 换行）
    with open(".env.debian", "w", newline="\n", encoding="utf-8") as f:
        f.write(env_text)

    print("✅  新的 JWT_SECRET / ANON_KEY / SERVICE_KEY 已写入 .env.debian")

if __name__ == "__main__":
    main()
